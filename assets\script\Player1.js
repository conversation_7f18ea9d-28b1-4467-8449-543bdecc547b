cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃力度
        jumpForce: 1500,
        
        // 最大下落高度，超过此高度视为游戏失败
        maxFallHeight: 500,
        
        // 最大跳跃次数
        maxJumpTimes: 5,
        
        // 玩家在X轴的固定位置
        fixedPositionX: -600,
        
        // // 音效
        // jumpSound: {
        //     default: null,
        //     type: cc.AudioClip
        // },
        
        // fallSound: {
        //     default: null,
        //     type: cc.AudioClip
        // }
    },

    onLoad() {
        
        
        // 初始化状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        
        // 获取刚体组件
        this.rigidBody = this.getComponent(cc.RigidBody);
        
        // 强制设置刚体为固定旋转
        this.rigidBody.fixedRotation = true;
        
        // 记录初始位置（用于重置）
        this.initialPosition = cc.v2(this.node.x, this.node.y);
        
        // 设置输入事件
        this.setupInputEvents();
        
        // 设置碰撞事件
        this.setupCollisionEvents();
    },

    start() {
        // 固定X轴位置
        this.node.x = this.fixedPositionX;
 
    },

    update(dt) {
        // 保持X轴位置不变
        this.node.x = this.fixedPositionX;
        
        // 检查是否超过最大下落高度
        if (!this.isGameOver && this.initialPosition.y - this.node.y > this.maxFallHeight) {
            this.gameOver();
        }
      
    },
    
    setupInputEvents() {
        // 设置触摸事件（移动设备）
        this.node.parent.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);

        // 设置鼠标点击事件（PC）
        this.node.parent.on(cc.Node.EventType.MOUSE_DOWN, this.onMouseDown, this);

        
    },
    
    onTouchStart(event) {
        this.jump();
    },

    onMouseDown(event) {
        this.jump();
    },
    
    setupCollisionEvents() {
        // 开启碰撞监听
        let collider = this.getComponent(cc.PhysicsBoxCollider);
        if (collider) {
            collider.sensor = false;

            // 确保启用了碰撞监听
            this.rigidBody.enabledContactListener = true;

            // 绑定碰撞事件
            this.node.on('onBeginContact', this.onBeginContact, this);
            this.node.on('onEndContact', this.onEndContact, this);

            console.log("碰撞事件已设置");
        } else {
            console.error("Player节点缺少PhysicsBoxCollider组件!");
        }
    },
    

    
    jump() {
        // 移除频繁的调试输出，只在需要时输出

        // 如果游戏结束，不允许跳跃
        if (this.isGameOver) {
          
            return;
        }

        // 跳跃条件：在地面上或者还有剩余跳跃次数
        if (this.isOnGround || this.jumpCount < this.maxJumpTimes) {
            // 直接设置速度 (这种方式比施加脉冲更可靠)
            this.rigidBody.linearVelocity = cc.v2(0, this.jumpForce);

            // 增加跳跃计数
            this.jumpCount++;

            // 标记不在地面上
            this.isOnGround = false;

            // 调试信息
          

            // 跳跃成功，可以在这里播放音效
            // if (this.jumpSound) {
            //     cc.audioEngine.playEffect(this.jumpSound, false);
            // }
        }
    },
    
    onBeginContact(contact, selfCollider, otherCollider) {
        let isPlatform = otherCollider.node.group === 'Ground';

        if (isPlatform) {
            // 简化地面检测：只要碰到地面且玩家向下运动或静止，就认为落地
            if (this.rigidBody.linearVelocity.y <= 0) {
                this.isOnGround = true;
                this.jumpCount = 0; // 重置跳跃次数
              
            }
        }
    },

    onEndContact(contact, selfCollider, otherCollider) {
        // 如果离开地面（通过分组检测）
        let isPlatform = otherCollider.node.group === 'Ground';

        if (isPlatform) {
            // 立即标记为离开地面，不使用延迟
            this.isOnGround = false;
        }
    },
    
    gameOver() {
        this.isGameOver = true;
        
        // 播放失败音效
        // if (this.fallSound) {
        //     cc.audioEngine.playEffect(this.fallSound, false);
        // }
        
        console.log('玩家游戏结束！');
        cc.director.pause(); // 游戏暂停
    },
    
    reset() {
        // 重置位置
        this.node.x = this.initialPosition.x;
        this.node.y = this.initialPosition.y;
        
        // 重置状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        
        // 重置速度
        if (this.rigidBody) {
            this.rigidBody.linearVelocity = cc.v2(0, 0);
        }
    },
    
    onDestroy() {
        // 移除事件监听
        this.node.parent.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.parent.off(cc.Node.EventType.MOUSE_DOWN, this.onMouseDown, this);

        // 移除碰撞事件监听
        this.node.off('onBeginContact', this.onBeginContact, this);
        this.node.off('onEndContact', this.onEndContact, this);
    }
});
